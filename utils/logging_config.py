"""
Logging Configuration Module
Centralized logging setup for Gmail registration system
"""

import logging
import logging.handlers
from datetime import datetime
from pathlib import Path
from typing import Optional


class ColoredFormatter(logging.Formatter):
    """Custom formatter with colors for console output"""
    
    # Color codes
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
        'RESET': '\033[0m'      # Reset
    }
    
    def format(self, record):
        # Add color to levelname
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


def setup_logging(
    log_level: str = "INFO",
    log_file: Optional[str] = None,
    log_dir: Optional[Path] = None,
    enable_console: bool = True,
    enable_file: bool = True,
    max_file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5
) -> logging.Logger:
    """
    Setup centralized logging configuration
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Log file name (default: registration.log)
        log_dir: Log directory path
        enable_console: Enable console logging
        enable_file: Enable file logging
        max_file_size: Maximum log file size in bytes
        backup_count: Number of backup files to keep
        
    Returns:
        Configured logger instance
    """
    # Convert string level to logging constant
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)
    
    # Create logger
    logger = logging.getLogger('gmail_registration')
    logger.setLevel(numeric_level)
    
    # Clear existing handlers to avoid duplicates
    logger.handlers.clear()
    
    # Create formatters
    console_formatter = ColoredFormatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )
    
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Console handler
    if enable_console:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(numeric_level)
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
    
    # File handler
    if enable_file:
        if log_dir is None:
            log_dir = Path.cwd() / "logs"
        
        # Ensure log directory exists
        log_dir.mkdir(parents=True, exist_ok=True)
        
        if log_file is None:
            log_file = "registration.log"
        
        log_path = log_dir / log_file
        
        # Use rotating file handler to manage log file size
        file_handler = logging.handlers.RotatingFileHandler(
            log_path,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(numeric_level)
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
    
    # Log the configuration
    logger.info(f"Logging configured - Level: {log_level}, Console: {enable_console}, File: {enable_file}")
    if enable_file:
        logger.info(f"Log file: {log_path}")
    
    return logger


def setup_detailed_logging(config) -> logging.Logger:
    """
    Setup detailed logging using configuration object
    
    Args:
        config: Configuration object with logging settings
        
    Returns:
        Configured logger instance
    """
    return setup_logging(
        log_level=config.LOG_LEVEL,
        log_file="registration.log",
        log_dir=config.LOGS_DIR,
        enable_console=True,
        enable_file=True,
        max_file_size=10 * 1024 * 1024,  # 10MB
        backup_count=5
    )


def create_session_logger(session_id: str, log_dir: Path) -> logging.Logger:
    """
    Create a session-specific logger for batch operations
    
    Args:
        session_id: Unique session identifier
        log_dir: Directory for log files
        
    Returns:
        Session-specific logger
    """
    logger_name = f'gmail_registration.session_{session_id}'
    logger = logging.getLogger(logger_name)
    
    # Don't propagate to parent logger to avoid duplicate messages
    logger.propagate = False
    logger.setLevel(logging.INFO)
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Create session log file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"session_{session_id}_{timestamp}.log"
    
    # File handler for session
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    file_handler.setFormatter(file_formatter)
    logger.addHandler(file_handler)
    
    logger.info(f"Session {session_id} logging started")
    return logger


def log_registration_attempt(logger: logging.Logger, account_id: int, user_data: dict, step: str):
    """
    Log registration attempt details
    
    Args:
        logger: Logger instance
        account_id: Account ID
        user_data: User data dictionary
        step: Current step in registration process
    """
    logger.info(f"Account {account_id} - Step: {step}")
    logger.info(f"Account {account_id} - Username: {user_data.get('username', 'N/A')}")
    logger.info(f"Account {account_id} - Full Name: {user_data.get('full_name', 'N/A')}")


def log_registration_result(logger: logging.Logger, account_id: int, result: dict):
    """
    Log registration result
    
    Args:
        logger: Logger instance
        account_id: Account ID
        result: Registration result dictionary
    """
    success = result.get('success', False)
    duration = result.get('duration_seconds', 0)
    
    if success:
        email = result.get('email', 'N/A')
        logger.info(f"Account {account_id} - SUCCESS: {email} (Duration: {duration:.1f}s)")
        
        # Log username conflicts if any
        username_conflicts = result.get('username_conflicts', {})
        if username_conflicts.get('username_changed', False):
            original = username_conflicts.get('original_username', 'N/A')
            final = username_conflicts.get('final_username', 'N/A')
            conflicts = username_conflicts.get('total_conflicts', 0)
            logger.info(f"Account {account_id} - Username changed: {original} → {final} ({conflicts} conflicts)")
    else:
        error = result.get('error', 'Unknown error')
        error_type = result.get('error_type', 'Unknown')
        logger.error(f"Account {account_id} - FAILED: {error_type} - {error} (Duration: {duration:.1f}s)")


def log_batch_summary(logger: logging.Logger, results: list):
    """
    Log batch operation summary
    
    Args:
        logger: Logger instance
        results: List of registration results
    """
    total = len(results)
    successful = len([r for r in results if r.get('success', False)])
    failed = total - successful
    success_rate = (successful / total) * 100 if total > 0 else 0
    
    logger.info(f"Batch Summary - Total: {total}, Success: {successful}, Failed: {failed}, Rate: {success_rate:.1f}%")
    
    # Log common error types
    error_types = {}
    for result in results:
        if not result.get('success', False):
            error_type = result.get('error_type', 'Unknown')
            error_types[error_type] = error_types.get(error_type, 0) + 1
    
    if error_types:
        logger.info("Error Types:")
        for error_type, count in error_types.items():
            logger.info(f"  - {error_type}: {count}")


# Configure root logger to prevent unwanted messages
logging.getLogger('playwright').setLevel(logging.WARNING)
logging.getLogger('urllib3').setLevel(logging.WARNING)
logging.getLogger('asyncio').setLevel(logging.WARNING)
