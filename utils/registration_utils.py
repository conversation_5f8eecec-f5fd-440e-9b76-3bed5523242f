"""
Registration Utilities Module
Contains helper functions for Gmail registration process
"""

import asyncio
import json
import logging
import random
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List
from playwright.async_api import Page


class RegistrationUtils:
    """Utility functions for Gmail registration"""
    
    def __init__(self, config):
        self.config = config
    
    async def random_delay(self, min_delay: float = 1.0, max_delay: float = 3.0):
        """Add random delay between actions to simulate human behavior"""
        delay = random.uniform(min_delay, max_delay)
        await asyncio.sleep(delay)
    
    async def take_screenshot(self, page: Page, account_id: int, step: str):
        """Take screenshot of current page state"""
        screenshot_path = self.config.SCREENSHOTS_DIR / f"account_{account_id}_{step}.png"
        await page.screenshot(path=str(screenshot_path))
        logging.info(f"Screenshot saved: {screenshot_path}")
    
    async def save_account_data(self, account_data: Dict[str, Any]):
        """Save account data to JSON file"""
        try:
            # Create filename with timestamp and account ID
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            account_id = account_data.get("account_id", "unknown")
            filename = f"account_{account_id}_{timestamp}.json"
            
            # Save to accounts directory
            filepath = self.config.ACCOUNTS_DIR / filename
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(account_data, f, indent=2, ensure_ascii=False)
                
            print(f"💾 Account data saved to {filepath}")
            
        except Exception as e:
            print(f"⚠️ Failed to save account data: {e}")
    
    async def save_batch_results(self, results: List[Dict[str, Any]]):
        """Save batch results to JSON file"""
        try:
            # Create filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"batch_results_{timestamp}.json"
            
            # Save to results directory
            filepath = self.config.RESULTS_DIR / filename
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
                
            print(f"💾 Batch results saved to {filepath}")
            
        except Exception as e:
            print(f"⚠️ Failed to save batch results: {e}")
    
    def calculate_registration_stats(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate statistics from registration results"""
        if not results:
            return {"total": 0, "successful": 0, "failed": 0, "success_rate": 0.0}
        
        total = len(results)
        successful = len([r for r in results if r.get("success", False)])
        failed = total - successful
        success_rate = (successful / total) * 100 if total > 0 else 0.0
        
        # Calculate average duration for successful registrations
        successful_results = [r for r in results if r.get("success", False) and r.get("duration_seconds")]
        avg_duration = sum(r["duration_seconds"] for r in successful_results) / len(successful_results) if successful_results else 0
        
        # Count username conflicts
        username_conflicts = len([r for r in results if r.get("username_conflicts", {}).get("username_changed", False)])
        
        # Common error types
        error_types = {}
        for result in results:
            if not result.get("success", False) and result.get("error"):
                error_type = result.get("error_type", "Unknown")
                error_types[error_type] = error_types.get(error_type, 0) + 1
        
        return {
            "total": total,
            "successful": successful,
            "failed": failed,
            "success_rate": round(success_rate, 2),
            "average_duration_seconds": round(avg_duration, 2),
            "username_conflicts": username_conflicts,
            "error_types": error_types
        }
    
    def print_registration_summary(self, results: List[Dict[str, Any]]):
        """Print a summary of registration results"""
        stats = self.calculate_registration_stats(results)
        
        print("\n" + "="*50)
        print("📊 REGISTRATION SUMMARY")
        print("="*50)
        print(f"Total Attempts: {stats['total']}")
        print(f"Successful: {stats['successful']}")
        print(f"Failed: {stats['failed']}")
        print(f"Success Rate: {stats['success_rate']}%")
        
        if stats['average_duration_seconds'] > 0:
            print(f"Average Duration: {stats['average_duration_seconds']}s")
        
        if stats['username_conflicts'] > 0:
            print(f"Username Conflicts Resolved: {stats['username_conflicts']}")
        
        if stats['error_types']:
            print("\nError Types:")
            for error_type, count in stats['error_types'].items():
                print(f"  - {error_type}: {count}")
        
        print("="*50)
    
    def validate_user_data(self, user_data: Dict[str, Any]) -> bool:
        """Validate user data before registration"""
        required_fields = [
            'first_name', 'last_name', 'username', 'password',
            'birth_year', 'birth_month', 'birth_day', 'gender'
        ]
        
        for field in required_fields:
            if field not in user_data or not user_data[field]:
                logging.error(f"Missing required field: {field}")
                return False
        
        # Validate birth date
        try:
            birth_year = int(user_data['birth_year'])
            birth_month = int(user_data['birth_month'])
            birth_day = int(user_data['birth_day'])
            
            current_year = datetime.now().year
            if birth_year < 1900 or birth_year > current_year - 13:  # Must be at least 13 years old
                logging.error(f"Invalid birth year: {birth_year}")
                return False
            
            if birth_month < 1 or birth_month > 12:
                logging.error(f"Invalid birth month: {birth_month}")
                return False
            
            if birth_day < 1 or birth_day > 31:
                logging.error(f"Invalid birth day: {birth_day}")
                return False
                
        except ValueError:
            logging.error("Birth date fields must be numeric")
            return False
        
        # Validate gender
        if user_data['gender'] not in ['Male', 'Female']:
            logging.error(f"Invalid gender: {user_data['gender']}")
            return False
        
        # Validate username format
        username = user_data['username']
        if len(username) < 6 or len(username) > 30:
            logging.error(f"Username length must be between 6-30 characters: {username}")
            return False
        
        # Check for invalid characters in username
        import re
        if not re.match(r'^[a-z0-9.]+$', username):
            logging.error(f"Username contains invalid characters: {username}")
            return False
        
        # Validate password strength
        password = user_data['password']
        if len(password) < 8:
            logging.error("Password must be at least 8 characters long")
            return False
        
        return True
    
    def sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for safe file operations"""
        import re
        # Remove or replace invalid characters
        sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # Remove multiple underscores
        sanitized = re.sub(r'_+', '_', sanitized)
        # Trim and remove leading/trailing underscores
        sanitized = sanitized.strip('_')
        return sanitized
    
    def ensure_directory_exists(self, directory_path: Path):
        """Ensure directory exists, create if it doesn't"""
        try:
            directory_path.mkdir(parents=True, exist_ok=True)
            logging.debug(f"Directory ensured: {directory_path}")
        except Exception as e:
            logging.error(f"Failed to create directory {directory_path}: {e}")
            raise
