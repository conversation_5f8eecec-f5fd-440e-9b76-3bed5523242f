"""
Gmail Registration Module
Handles the core Gmail registration process
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, Any
import logging
from playwright.async_api import Page

from ..browser.browser_actions import BrowserActions
from ..utils.delay_utils import random_delay
from ..utils.screenshot_utils import take_screenshot
from ..utils.username_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from constants import Messages, Timeouts

class GmailRegistration:
    def __init__(self, browser_actions: BrowserActions):
        self.browser_actions = browser_actions
        self.username_handler = UsernameHandler()
        
    async def register_account(self, user_data: Dict[str, Any], browser_profile: Dict[str, Any], account_id: int) -> Dict[str, Any]:
        """
        Register a single Gmail account
        
        Args:
            user_data: User data dictionary
            browser_profile: Browser profile settings
            account_id: Account ID for tracking
            
        Returns:
            Dict containing registration results
        """
        start_time = datetime.now()
        logging.info(f"{Messages.CONSOLE['registration_start']} #{account_id}")
        
        try:
            result = await self._perform_registration(user_data, browser_profile, account_id)
            
            # Calculate duration
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            result["duration_seconds"] = duration
            result["account_id"] = account_id
            
            if result["success"]:
                logging.info(f"{Messages.CONSOLE['registration_success']} #{account_id} in {duration:.1f}s")
                logging.info(f"📧 Email: {result.get('email', 'N/A')}")
            else:
                logging.error(f"{Messages.CONSOLE['registration_failed']} #{account_id}: {result.get('error', Messages.ERRORS['unknown_error'])}")
            
            return result
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "account_id": account_id,
                "timestamp": start_time.isoformat(),
                "duration_seconds": (datetime.now() - start_time).total_seconds()
            }

    async def _perform_registration(self, user_data: Dict[str, Any], browser_profile: Dict[str, Any], account_id: int) -> Dict[str, Any]:
        """Execute the registration process using browser actions"""
        page = await self.browser_actions.setup_browser(browser_profile)
        
        try:
            # Step 1: Navigate to signup page
            await self.browser_actions.navigate_to_signup(page)
            await take_screenshot(page, account_id, "step1")
            
            # Step 2: Fill basic information
            await self._fill_basic_info(page, user_data)
            await take_screenshot(page, account_id, "step2")
            
            # Step 3: Fill personal information
            await self._fill_personal_info(page, user_data)
            await take_screenshot(page, account_id, "step3")
            
            # Step 4: Handle username and password
            username_result = await self.username_handler.handle_username_setup(page, user_data, account_id)
            if not username_result["success"]:
                return username_result
            
            # Step 5: Complete registration
            return await self._complete_registration(page, user_data)
            
        except Exception as e:
            logging.error(f"Registration error: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "account_id": account_id
            }
        finally:
            await self.browser_actions.cleanup()

    async def _fill_basic_info(self, page: Page, user_data: Dict[str, Any]):
        """Fill basic user information"""
        await page.get_by_role('textbox', name='First name').fill(user_data['first_name'])
        await random_delay()
        
        await page.get_by_role('textbox', name='Last name (optional)').fill(user_data['last_name'])
        await random_delay()
        
        await page.get_by_role('button', name='Next').click()
        await random_delay()

    async def _fill_personal_info(self, page: Page, user_data: Dict[str, Any]):
        """Fill personal information like birth date and gender"""
        # Month selection
        month_mapping = self._get_month_mapping()
        user_month = month_mapping.get(str(user_data['birth_month']), str(user_data['birth_month']))
        await page.get_by_role('combobox', name='Month').click()
        await page.get_by_role('option', name=user_month).click()
        await random_delay()

        # Day and Year
        await page.get_by_role('textbox', name='Day').fill(str(user_data['birth_day']))
        await random_delay()
        
        await page.get_by_role('textbox', name='Year').fill(str(user_data['birth_year']))
        await random_delay()

        # Gender selection
        await page.get_by_role('combobox', name='Gender').click()
        await random_delay()
        
        await page.get_by_role('option', name=user_data['gender'], exact=True).click()
        await random_delay()

        await page.get_by_role('button', name='Next').click()
        await random_delay()

    async def _complete_registration(self, page: Page, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Complete the final steps of registration"""
        try:
            # Additional steps like phone verification would go here
            return {
                "success": True,
                "email": f"{user_data['username']}@gmail.com",
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def _get_month_mapping(self) -> Dict[str, str]:
        """Get month mapping for different locales and formats"""
        return {
            "January": "January", "February": "February", "March": "March",
            "April": "April", "May": "May", "June": "June",
            "July": "July", "August": "August", "September": "September",
            "October": "October", "November": "November", "December": "December",
            "Jan": "January", "Feb": "February", "Mar": "March",
            "Apr": "April", "Jun": "June", "Jul": "July",
            "Aug": "August", "Sep": "September", "Oct": "October",
            "Nov": "November", "Dec": "December",
            "1": "January", "2": "February", "3": "March",
            "4": "April", "5": "May", "6": "June",
            "7": "July", "8": "August", "9": "September",
            "10": "October", "11": "November", "12": "December"
        } 