"""
Browser Actions Module
Handles browser setup and actions
"""

from typing import Dict, Any, Optional
import logging
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>er<PERSON><PERSON>x<PERSON>, Playwright

from constants import Messages, URLs

class BrowserActions:
    def __init__(self):
        self.playwright: Optional[Playwright] = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None

    async def setup_browser(self, browser_profile: Dict[str, Any]) -> Page:
        """
        Setup browser with stealth mode and custom profile

        Args:
            browser_profile: Browser configuration dictionary

        Returns:
            Playwright Page object
        """
        logging.info(Messages.CONSOLE["browser_start"])

        # Initialize playwright
        self.playwright = await async_playwright().start()

        # Launch browser with stealth mode
        self.browser = await self.playwright.chromium.launch(
            headless=False,  # Set to True for production
            proxy=browser_profile.get('proxy'),
            args=[
                '--disable-blink-features=AutomationControlled',
                '--disable-features=IsolateOrigins,site-per-process',
                '--disable-site-isolation-trials',
                f'--window-size={browser_profile["viewport"]["width"]},{browser_profile["viewport"]["height"]}',
                '--start-maximized'
            ]
        )

        # Create new context with custom viewport and timezone
        self.context = await self.browser.new_context(
            viewport=browser_profile['viewport'],
            timezone_id=browser_profile['timezone_id'],
            user_agent=browser_profile['user_agent'],
            locale=browser_profile['locale'],
            geolocation=browser_profile.get('geolocation'),
            permissions=['geolocation']
        )

        # Add stealth scripts
        await self.add_stealth_scripts()

        # Create and return new page
        return await self.context.new_page()
    
    async def add_stealth_scripts(self):
        """Add anti-detection scripts to browser context"""
        await self.context.add_init_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined
            });
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en']
            });
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5]
            });
        """)
    
    async def navigate_to_signup(self, page: Page):
        """Navigate to Gmail signup page"""
        logging.info(Messages.CONSOLE["nav_signup"])
        await page.goto(URLs.GMAIL_SIGNUP)
        await page.wait_for_load_state('networkidle', timeout=10000)
    
    async def cleanup(self):
        """Clean up browser resources"""
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()