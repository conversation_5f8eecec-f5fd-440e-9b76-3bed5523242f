"""
Username Handler Module
Handles username selection and conflict resolution
"""

import re
import random
import logging
from typing import Dict, Any
from playwright.async_api import Page
from datetime import datetime

from .delay_utils import random_delay
from .screenshot_utils import take_screenshot

class UsernameHandler:
    async def handle_username_setup(self, page: Page, user_data: Dict[str, Any], account_id: int) -> Dict[str, Any]:
        """
        Handle username selection and conflict resolution
        
        Args:
            page: Playwright Page object
            user_data: User data dictionary
            account_id: Account ID for tracking
            
        Returns:
            Dict containing username handling results
        """
        original_username = user_data['username']
        username_conflict_history = []
        
        try:
            # Check if we're on username selection page
            if await self._is_username_suggestion_page(page):
                logging.info("On username suggestion page")
                
                # Try to use custom username first
                if await self._try_custom_username(page, user_data):
                    return {"success": True, "username": user_data['username']}
                
                # If custom username fails, try suggestions
                suggestion_result = await self._handle_username_suggestions(page, user_data)
                if suggestion_result["success"]:
                    return suggestion_result
                
                # If both fail, generate fallback username
                return await self._generate_fallback_username(page, user_data, original_username)
            
            return {"success": True, "username": user_data['username']}
            
        except Exception as e:
            logging.error(f"Username setup error: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "username": original_username
            }
    
    async def _is_username_suggestion_page(self, page: Page) -> bool:
        """Check if we're on the username suggestion page"""
        page_title = await page.title()
        return "Choose your Gmail address" in page_title or "Pick a Gmail address" in page_title
    
    async def _try_custom_username(self, page: Page, user_data: Dict[str, Any]) -> bool:
        """Try to use custom username"""
        try:
            # Look for create own option
            create_own_selectors = [
                'text="Create your own Gmail address"',
                'text="Create a different address"',
                '[aria-label="Create your own Gmail address"]',
                'button:has-text("Create")',
                'text="Create"'
            ]
            
            for selector in create_own_selectors:
                try:
                    element = page.locator(selector)
                    if await element.is_visible(timeout=2000):
                        await element.click()
                        await random_delay()
                        
                        # Fill username
                        username_field = page.get_by_label('Username')
                        await username_field.fill(user_data['username'])
                        await random_delay()
                        return True
                except:
                    continue
                    
            return False
            
        except Exception as e:
            logging.error(f"Custom username error: {str(e)}")
            return False
    
    async def _handle_username_suggestions(self, page: Page, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle username suggestions from Google"""
        try:
            suggestion_selectors = [
                'button[data-value*="@gmail.com"]',
                '[role="button"]:has-text("@gmail.com")',
                'button:has-text("available")',
                '.username-suggestion',
                'button[jsaction]'
            ]
            
            for selector in suggestion_selectors:
                try:
                    suggestions = page.locator(selector)
                    count = await suggestions.count()
                    
                    if count > 0:
                        first_suggestion = suggestions.first
                        suggestion_text = await first_suggestion.text_content()
                        await first_suggestion.click()
                        await random_delay()
                        
                        # Update username
                        if '@gmail.com' in suggestion_text:
                            user_data['username'] = suggestion_text.replace('@gmail.com', '')
                        else:
                            user_data['username'] = suggestion_text
                            
                        return {
                            "success": True,
                            "username": user_data['username'],
                            "was_suggestion": True
                        }
                except:
                    continue
                    
            return {"success": False, "error": "No valid suggestions found"}
            
        except Exception as e:
            logging.error(f"Username suggestion error: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _generate_fallback_username(self, page: Page, user_data: Dict[str, Any], original_username: str) -> Dict[str, Any]:
        """Generate fallback username when other methods fail"""
        try:
            # Clean username
            clean_username = re.sub(r'[^a-z0-9.]', '', original_username.lower())
            
            # Add random numbers if needed
            if len(clean_username) < 6:
                clean_username += str(random.randint(100, 999))
                
            # Try the clean username
            username_field = page.get_by_label('Username')
            await username_field.fill(clean_username)
            await random_delay()
            
            # Check for errors
            error_elements = page.locator('[role="alert"], .error-message')
            error_count = await error_elements.count()
            
            if error_count > 0:
                # Add timestamp to make unique
                timestamp = datetime.now().strftime("%Y%m%d")
                clean_username = f"{clean_username}{timestamp}"
                
                await username_field.fill(clean_username)
                await random_delay()
            
            user_data['username'] = clean_username
            return {
                "success": True,
                "username": clean_username,
                "was_fallback": True
            }
            
        except Exception as e:
            logging.error(f"Fallback username error: {str(e)}")
            return {"success": False, "error": str(e)} 