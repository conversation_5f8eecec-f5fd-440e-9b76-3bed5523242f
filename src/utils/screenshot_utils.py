"""
Screenshot Utilities Module
Handles screenshot capture and storage
"""

import os
from datetime import datetime
from pathlib import Path
from typing import Op<PERSON>
from playwright.async_api import Page

from config import Config

async def take_screenshot(page: Page, account_id: int, step: str, error: bool = False):
    """
    Take a screenshot of the current page state
    
    Args:
        page: Playwright Page object
        account_id: Account ID for the screenshot
        step: Step identifier
        error: Whether this is an error screenshot
    """
    if not Config.SAVE_SCREENSHOTS:
        return
        
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        prefix = "error" if error else "step"
        filename = f"account_{account_id}_{prefix}_{step}_{timestamp}.png"
        
        # Ensure screenshots directory exists
        screenshots_dir = Config.SCREENSHOTS_DIR
        screenshots_dir.mkdir(parents=True, exist_ok=True)
        
        # Take screenshot
        filepath = screenshots_dir / filename
        await page.screenshot(path=str(filepath))
        
    except Exception as e:
        print(f"Failed to take screenshot: {str(e)}")

async def save_page_source(page: Page, account_id: int, step: str):
    """
    Save the current page HTML source
    
    Args:
        page: Playwright Page object
        account_id: Account ID for the file
        step: Step identifier
    """
    if not Config.SAVE_PAGE_HTML:
        return
        
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"account_{account_id}_page_{step}_{timestamp}.html"
        
        # Create html directory if it doesn't exist
        html_dir = Config.DATA_DIR / "html"
        html_dir.mkdir(parents=True, exist_ok=True)
        
        # Save HTML content
        filepath = html_dir / filename
        content = await page.content()
        filepath.write_text(content, encoding='utf-8')
        
    except Exception as e:
        print(f"Failed to save page source: {str(e)}")

def cleanup_old_screenshots(max_age_days: int = 7):
    """
    Remove screenshots older than specified days
    
    Args:
        max_age_days: Maximum age of screenshots to keep
    """
    try:
        screenshots_dir = Config.SCREENSHOTS_DIR
        if not screenshots_dir.exists():
            return
            
        current_time = datetime.now()
        for file in screenshots_dir.glob("*.png"):
            file_time = datetime.fromtimestamp(file.stat().st_mtime)
            age_days = (current_time - file_time).days
            
            if age_days > max_age_days:
                file.unlink()
                
    except Exception as e:
        print(f"Failed to cleanup screenshots: {str(e)}") 