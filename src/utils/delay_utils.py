"""
Delay Utilities Module
Provides functions for realistic delays and timing
"""

import asyncio
import random
from typing import Optional
from constants import Timeouts

async def random_delay(min_delay: Optional[float] = None, max_delay: Optional[float] = None):
    """
    Add a random delay to simulate human behavior
    
    Args:
        min_delay: Minimum delay in seconds
        max_delay: Maximum delay in seconds
    """
    min_d = min_delay if min_delay is not None else Timeouts.HUMAN_ACTION["min"]
    max_d = max_delay if max_delay is not None else Timeouts.HUMAN_ACTION["max"]
    delay = random.uniform(min_d, max_d)
    await asyncio.sleep(delay)

async def typing_delay():
    """Add a random delay to simulate human typing"""
    delay = random.uniform(
        Timeouts.HUMAN_TYPING["min"],
        Timeouts.HUMAN_TYPING["max"]
    )
    await asyncio.sleep(delay)

async def reading_delay():
    """Add a random delay to simulate reading time"""
    delay = random.uniform(
        Timeouts.HUMAN_READING["min"],
        Timeouts.HUMAN_READING["max"]
    )
    await asyncio.sleep(delay)

async def thinking_delay():
    """Add a random delay to simulate thinking time"""
    delay = random.uniform(
        Timeouts.HUMAN_THINKING["min"],
        Timeouts.HUMAN_THINKING["max"]
    )
    await asyncio.sleep(delay) 