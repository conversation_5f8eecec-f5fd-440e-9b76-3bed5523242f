"""
Gmail Registration Orchestrator Module
Main orchestrator for the Gmail registration system
"""

import asyncio
import logging
from datetime import datetime
from typing import List, Dict, Any
from dotenv import load_dotenv

from config import Config
from constants import Messages
from user_data_generator import UserDataGenerator
from proxy_manager import ProxyManager
from browser_profile import BrowserProfileGenerator
from automation import GmailAutomation
from utils import RegistrationUtils, setup_detailed_logging


class GmailRegistrationOrchestrator:
    """Main orchestrator for Gmail registration system"""
    
    def __init__(self):
        self.config = Config
        self.proxy_manager = None
        self.user_generator = UserDataGenerator()
        self.browser_generator = BrowserProfileGenerator()
        self.gmail_automation = GmailAutomation(self.config)
        self.utils = RegistrationUtils(self.config)
        self.results = []
        load_dotenv()
        
        # Setup logging
        self.logger = setup_detailed_logging(self.config)
        
    async def initialize(self) -> bool:
        """
        Initialize the entire system
        
        Returns:
            bool: True if initialization successful
        """
        try:
            print(Messages.CONSOLE["init_start"])
            
            # Ensure directories exist
            self.config.ensure_directories()
            
            # Validate configuration
            if not self.config.validate_config():
                return False
            
            # Force disable proxy for testing
            self.config.USE_PROXY = False

            # Initialize proxy manager
            if self.config.USE_PROXY:
                self.proxy_manager = ProxyManager()
                if not await self.proxy_manager.initial_setup():
                    print(Messages.CONSOLE["proxy_failed"])
                    return False
            else:
                print(Messages.CONSOLE["proxy_disabled"])
            
            # Print configuration summary
            self.config.print_config_summary()
            
            print(Messages.CONSOLE["init_success"])
            return True
            
        except Exception as e:
            print(f"{Messages.CONSOLE['init_failed']}: {e}")
            return False
    
    async def register_single_account(self, account_id: int = 1) -> Dict[str, Any]:
        """
        Register a single Gmail account
        
        Args:
            account_id: Account ID (for logging)
            
        Returns:
            Dict containing registration result
        """
        start_time = datetime.now()
        print(f"\n{Messages.CONSOLE['registration_start']} #{account_id}")
        
        try:
            # Generate user data
            user_data = self.user_generator.generate_user_info()
            print(f"{Messages.CONSOLE['user_generated']}: {user_data['full_name']} ({user_data['username']})")
            
            # Validate user data
            if not self.utils.validate_user_data(user_data):
                return {
                    "success": False,
                    "error": "Invalid user data generated",
                    "account_id": account_id,
                    "user_data": user_data,
                    "timestamp": start_time.isoformat()
                }
            
            # Get proxy if enabled
            proxy_config = None
            if self.proxy_manager:
                proxy_config = await self.proxy_manager.get_working_proxy()
                if not proxy_config:
                    return {
                        "success": False,
                        "error": Messages.ERRORS["no_proxy"],
                        "account_id": account_id,
                        "user_data": user_data,
                        "timestamp": start_time.isoformat()
                    }
                print(f"{Messages.CONSOLE['proxy_using']}: {proxy_config.server}")
            
            # Create browser profile
            browser_profile = self.browser_generator.create_human_browser_profile(proxy_config)
            print(f"🌍 Browser timezone: {browser_profile['timezone_id']}")
            print(f"📱 Viewport: {browser_profile['viewport']}")
            
            # Start registration process
            result = await self.gmail_automation.perform_registration(user_data, browser_profile, account_id)
            
            # Calculate duration
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            result["duration_seconds"] = duration
            result["account_id"] = account_id
            
            if result["success"]:
                print(f"{Messages.CONSOLE['registration_success']} #{account_id} in {duration:.1f}s")
                print(f"📧 Email: {result.get('email', 'N/A')}")
            else:
                print(f"{Messages.CONSOLE['registration_failed']} #{account_id}: {result.get('error', Messages.ERRORS['unknown_error'])}")
            
            # Save account data
            await self.utils.save_account_data(result)
            
            return result
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "account_id": account_id,
                "timestamp": start_time.isoformat(),
                "duration_seconds": (datetime.now() - start_time).total_seconds()
            }
    
    async def register_batch_accounts(self, num_accounts: int, max_concurrent: int = None) -> List[Dict[str, Any]]:
        """
        Register multiple accounts concurrently
        
        Args:
            num_accounts: Number of accounts to register
            max_concurrent: Maximum concurrent registrations
            
        Returns:
            List of registration results
        """
        if max_concurrent is None:
            max_concurrent = self.config.MAX_CONCURRENT_REGISTRATIONS
            
        print(f"\n🚀 Starting batch registration of {num_accounts} accounts")
        print(f"⚙️ Max concurrent: {max_concurrent}")
        print(f"⏱️ Delay between registrations: {self.config.DELAY_BETWEEN_REGISTRATIONS}s")
        
        # Create semaphore to limit concurrent registrations
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def register_with_semaphore(account_id: int):
            async with semaphore:
                result = await self.register_single_account(account_id)
                
                # Delay between registrations
                if account_id < num_accounts:
                    delay = self.config.DELAY_BETWEEN_REGISTRATIONS
                    print(f"⏰ Waiting {delay}s before next registration...")
                    await asyncio.sleep(delay)
                
                return result
        
        # Create tasks for all accounts
        tasks = []
        for i in range(1, num_accounts + 1):
            tasks.append(register_with_semaphore(i))
            
        # Run all tasks concurrently and wait for completion
        results = await asyncio.gather(*tasks)
        
        # Save batch results
        await self.utils.save_batch_results(results)
        
        # Print summary
        self.utils.print_registration_summary(results)
        
        return results
    
    def get_registration_stats(self) -> Dict[str, Any]:
        """Get current registration statistics"""
        return self.utils.calculate_registration_stats(self.results)
    
    async def test_system_components(self) -> Dict[str, bool]:
        """Test all system components"""
        results = {
            "config": False,
            "user_generator": False,
            "browser_profile": False,
            "proxy_manager": False,
            "directories": False
        }
        
        try:
            # Test configuration
            results["config"] = self.config.validate_config()
            
            # Test user data generation
            user_data = self.user_generator.generate_user_info()
            results["user_generator"] = self.utils.validate_user_data(user_data)
            
            # Test browser profile generation
            profile = self.browser_generator.create_human_browser_profile()
            results["browser_profile"] = bool(profile and profile.get('user_agent'))
            
            # Test proxy manager if enabled
            if self.config.USE_PROXY and self.proxy_manager:
                proxy = await self.proxy_manager.get_working_proxy()
                results["proxy_manager"] = proxy is not None
            else:
                results["proxy_manager"] = True  # Not required
            
            # Test directory creation
            self.config.ensure_directories()
            results["directories"] = all([
                self.config.DATA_DIR.exists(),
                self.config.LOGS_DIR.exists(),
                self.config.SCREENSHOTS_DIR.exists(),
                self.config.ACCOUNTS_DIR.exists()
            ])
            
        except Exception as e:
            self.logger.error(f"Error testing system components: {e}")
        
        return results
