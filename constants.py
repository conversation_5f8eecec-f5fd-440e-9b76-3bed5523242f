"""
Constants and Hardcoded Values
Centralizes all constants, URLs, timeouts, and magic numbers to avoid hardcoding throughout the project.
"""

from typing import List, Dict, Any

# ==============================================
# URLs & Endpoints
# ==============================================
class URLs:
    GMAIL_SIGNUP = "https://accounts.google.com/signup"
    GMAIL_LOGIN = "https://accounts.google.com/signin"
    GMAIL_RECOVERY = "https://accounts.google.com/signin/recovery"
    GOOGLE_MAIN = "https://www.google.com"
    
    # Test URLs
    IP_CHECK_SERVICES = [
        "https://httpbin.org/ip",
        "https://api.ipify.org?format=json",
        "https://ipinfo.io/json"
    ]

# ==============================================
# Timeouts & Delays (seconds)
# ==============================================
class Timeouts:
    """Timeout constants in milliseconds"""
    BROWSER_DEFAULT = 30000
    NAVIGATION = 60000  # Increased from 30
    FORM_FILL = 20000
    CAPTCHA_SOLVE = 180000
    VERIFICATION = 180
    PROXY_TEST = 10
    
    # Human delays (min, max ranges)
    HUMAN_ACTION = {"min": 1.0, "max": 3.0}
    HUMAN_TYPING = {"min": 0.05, "max": 0.25}
    HUMAN_READING = {"min": 2.0, "max": 5.0}
    HUMAN_THINKING = {"min": 3.0, "max": 8.0}
    
    # Registration delays
    BETWEEN_REGISTRATIONS = 300
    RETRY_DELAY = 60
    BATCH_PROCESSING = {"min": 10, "max": 30}

# ==============================================
# Browser Configuration
# ==============================================
class Browser:
    # Default viewport
    DEFAULT_VIEWPORT = {"width": 1366, "height": 768}
    
    # Common viewport sizes
    VIEWPORT_SIZES = [
        {"width": 1366, "height": 768},   # Laptop common
        {"width": 1920, "height": 1080},  # Desktop HD
        {"width": 1440, "height": 900},   # MacBook
        {"width": 1536, "height": 864},   # Surface
        {"width": 1280, "height": 720},   # Smaller laptop
        {"width": 1024, "height": 768}    # Old standard
    ]
    
    # Chrome arguments for stealth
    CHROME_ARGS = [
        '--disable-blink-features=AutomationControlled',
        '--exclude-switches=enable-automation',
        '--disable-extensions-file-access-check',
        '--disable-extensions-http-throttling',
        '--disable-extensions-https-throttling',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-features=TranslateUI',
        '--disable-component-extensions-with-background-pages',
        '--disable-default-apps',
        '--disable-sync',
        '--metrics-recording-only',
        '--no-first-run',
        '--disable-web-security',
        '--allow-running-insecure-content'
    ]
    
    # Memory settings ranges
    MEMORY_SETTINGS = {
        "max_old_space_size": {"min": 2048, "max": 4096},
        "max_heap_size": {"min": 1024, "max": 2048}
    }
    
    # Device scale factors
    DEVICE_SCALE_FACTORS = [1, 1.25, 1.5, 2]
    
    # Fallback User Agent
    FALLBACK_USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

# ==============================================
# User Data Generation
# ==============================================
class UserData:
    # American names (extended lists)
    AMERICAN_FIRST_NAMES_MALE = [
        "James", "Robert", "John", "Michael", "William", "David", "Richard", "Joseph",
        "Thomas", "Christopher", "Charles", "Daniel", "Matthew", "Anthony", "Mark", "Donald",
        "Steven", "Andrew", "Kenneth", "Joshua", "Kevin", "Brian", "George", "Timothy",
        "Ronald", "Jason", "Edward", "Jeffrey", "Ryan", "Jacob", "Gary", "Nicholas",
        "Eric", "Jonathan", "Stephen", "Larry", "Justin", "Scott", "Brandon", "Benjamin"
    ]
    
    AMERICAN_FIRST_NAMES_FEMALE = [
        "Mary", "Patricia", "Jennifer", "Linda", "Elizabeth", "Barbara", "Susan", "Jessica",
        "Sarah", "Karen", "Nancy", "Lisa", "Betty", "Helen", "Sandra", "Donna",
        "Carol", "Ruth", "Sharon", "Michelle", "Laura", "Kimberly", "Deborah", "Dorothy",
        "Amy", "Angela", "Ashley", "Brenda", "Emma", "Olivia", "Cynthia", "Marie",
        "Janet", "Catherine", "Frances", "Christine", "Samantha", "Debra", "Rachel", "Carolyn"
    ]
    
    AMERICAN_LAST_NAMES = [
        "Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis",
        "Rodriguez", "Martinez", "Hernandez", "Lopez", "Gonzalez", "Wilson", "Anderson", "Thomas",
        "Taylor", "Moore", "Jackson", "Martin", "Lee", "Perez", "Thompson", "White",
        "Harris", "Sanchez", "Clark", "Ramirez", "Lewis", "Robinson", "Walker", "Young",
        "Allen", "King", "Wright", "Scott", "Torres", "Nguyen", "Hill", "Flores",
        "Green", "Adams", "Nelson", "Baker", "Hall", "Rivera", "Campbell", "Mitchell",
        "Carter", "Roberts", "Gomez", "Phillips", "Evans", "Turner", "Diaz", "Parker"
    ]
    
    # Email domains for recovery emails
    COMMON_EMAIL_DOMAINS = [
        "gmail.com", "yahoo.com", "hotmail.com", "outlook.com", "aol.com",
        "icloud.com", "protonmail.com", "live.com", "msn.com", "comcast.net",
        "verizon.net", "att.net", "sbcglobal.net", "cox.net"
    ]
    
    # US Cities
    US_CITIES = [
        "New York", "Los Angeles", "Chicago", "Houston", "Phoenix", "Philadelphia",
        "San Antonio", "San Diego", "Dallas", "San Jose", "Austin", "Jacksonville",
        "Fort Worth", "Columbus", "Charlotte", "San Francisco", "Indianapolis", "Seattle",
        "Denver", "Washington", "Boston", "El Paso", "Nashville", "Detroit", "Oklahoma City",
        "Portland", "Las Vegas", "Memphis", "Louisville", "Baltimore", "Milwaukee"
    ]
    
    # US Area codes
    US_AREA_CODES = [
        '202', '212', '213', '214', '305', '310', '312', '323', '415', '510',
        '602', '702', '713', '714', '718', '773', '818', '917', '949', '954',
        '972', '404', '407', '480', '503', '512', '516', '561', '615', '619',
        '678', '720', '757', '832', '858', '901', '925', '941', '972', '980'
    ]
    
    # Password generation settings
    PASSWORD_CONFIG = {
        "min_length": 8,
        "default_length": 12,
        "max_length": 20,
        "special_chars": "!@#$%^&*",
        "include_uppercase": True,
        "include_lowercase": True,
        "include_digits": True,
        "include_special": True
    }
    
    # Age ranges for realistic birth dates
    AGE_RANGE = {"min": 18, "max": 35}

# ==============================================
# Localization & Timezone
# ==============================================
class Localization:
    # US Timezones
    US_TIMEZONES = [
        'America/New_York',      # Eastern Time
        'America/Chicago',       # Central Time
        'America/Denver',        # Mountain Time
        'America/Los_Angeles',   # Pacific Time
        'America/Phoenix',       # Arizona Time
        'America/Detroit',       # Michigan Time
        'America/Anchorage',     # Alaska Time
        'America/Honolulu'       # Hawaii Time
    ]
    
    # Language preferences
    LANGUAGES = {
        "primary": "en-US",
        "secondary": ["en", "es-US", "es"],
        "accept_language": "en-US,en;q=0.9,es-US;q=0.8,es;q=0.7"
    }
    
    # Locale settings
    LOCALE = "en-US"

# ==============================================
# HTTP Headers & Network
# ==============================================
class Headers:
    # Common HTTP headers template
    REALISTIC_HEADERS = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': Localization.LANGUAGES["accept_language"],
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-User': '?1',
        'Sec-Fetch-Dest': 'document',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
    }
    
    # Chrome version ranges for sec-ch-ua header
    CHROME_VERSION_RANGE = {"min": 100, "max": 130}

# ==============================================
# Stealth & Anti-Detection
# ==============================================
class AntiDetection:
    # JavaScript scripts for stealth mode
    STEALTH_SCRIPTS = {
        "webdriver_removal": """
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
        """,
        
        "plugins_fake": """
            Object.defineProperty(navigator, 'plugins', {
                get: () => [
                    {
                        name: 'Chrome PDF Plugin',
                        filename: 'internal-pdf-viewer',
                        description: 'Portable Document Format'
                    },
                    {
                        name: 'Chrome PDF Viewer',
                        filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai',
                        description: ''
                    },
                    {
                        name: 'Native Client',
                        filename: 'internal-nacl-plugin',
                        description: ''
                    }
                ],
            });
        """,
        
        "languages_fake": """
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en', 'es-US', 'es'],
            });
        """,
        
        "permissions_fake": """
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
        """,
        
        "chrome_runtime": """
            window.chrome = {
                runtime: {}
            };
        """
    }
    
    # Mouse movement patterns
    MOUSE_PATTERNS = {
        "natural_speed": {"min": 0.5, "max": 2.0},
        "movement_steps": {"min": 5, "max": 15},
        "randomness_factor": 0.1
    }
    
    # Human behavior rates
    HUMAN_BEHAVIOR = {
        "mistake_rate": 0.05,  # 5% chance of typos
        "reading_wpm": {"min": 200, "max": 300},  # Words per minute reading speed
        "typing_wpm": {"min": 30, "max": 80}      # Words per minute typing speed
    }

# ==============================================
# File Paths & Storage
# ==============================================
class Paths:
    # Default directory names
    DIRECTORIES = {
        "data": "data",
        "logs": "logs", 
        "screenshots": "screenshots",
        "conversations": "conversations",
        "accounts": "accounts",
        "backups": "backups",
        "temp": "temp"
    }
    
    # File names
    FILES = {
        "accounts_db": "gmail_accounts.db",
        "analytics_db": "analytics.db",
        "config_backup": "config_backup.json",
        "session_state": "session_state.json"
    }

# ==============================================
# Success & Error Tracking
# ==============================================
class Analytics:
    # Success rate thresholds
    SUCCESS_THRESHOLDS = {
        "excellent": 0.95,
        "good": 0.80,
        "acceptable": 0.60,
        "poor": 0.40
    }
    
    # Monitoring intervals (seconds)
    MONITORING = {
        "health_check": 300,    # 5 minutes
        "proxy_rotation": 600,  # 10 minutes
        "cleanup": 3600         # 1 hour
    }

# ==============================================
# Messages & UI Text
# ==============================================
class Messages:
    # Console output messages
    CONSOLE = {
        "init_start": "🚀 Initializing Gmail Auto Registration System...",
        "init_success": "✅ System initialization completed successfully",
        "init_failed": "❌ Initialization failed",
        "proxy_failed": "❌ Proxy initialization failed",
        "proxy_disabled": "🚫 Running without proxies",
        "registration_start": "🎯 Starting Gmail registration for account",
        "registration_success": "✅ Account registered successfully",
        "registration_failed": "❌ Account registration failed",
        "user_generated": "👤 Generated user",
        "proxy_using": "🌐 Using proxy",
        "browser_start": "🌐 Starting browser session...",
        "nav_signup": "📍 Navigating to Gmail signup page...",
        "form_fill": "📝 Filling registration form...",
        "captcha_handle": "🤖 Handling captcha...",
        "verification": "📱 Processing verification..."
    }
    
    # Error messages
    ERRORS = {
        "no_proxy": "No working proxy available",
        "registration_failed": "Registration failed",
        "config_invalid": "Configuration validation failed",
        "unknown_error": "Unknown error occurred"
    }

# ==============================================
# Version & Build Info
# ==============================================
class BuildInfo:
    VERSION = "1.0.0"
    BUILD_DATE = "2024-01-15"
    AUTHOR = "Gmail Auto Registration System"
    DESCRIPTION = "Automated Gmail account registration using browser-use library" 