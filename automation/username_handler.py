"""
Username Conflict Handler Module
Handles username conflicts during Gmail registration
"""

import asyncio
import logging
import random
import time
from typing import Dict, Any
from playwright.async_api import Page


class UsernameConflictHandler:
    """Handles username conflicts and resolution strategies"""
    
    async def handle_username_conflict(self, page: Page, user_data: Dict[str, Any], account_id: int) -> Dict[str, Any]:
        """
        Enhanced username conflict detection and resolution

        Args:
            page: Playwright page object
            user_data: User data dictionary containing username
            account_id: Account ID for logging

        Returns:
            Dict containing conflict resolution results
        """
        original_username = user_data['username']
        result = {
            'username_changed': False,
            'original_username': original_username,
            'final_username': original_username,
            'conflict_detected': False,
            'resolution_method': None,
            'suggestions_found': 0
        }

        try:
            # Wait for any error messages or suggestions to appear
            await asyncio.sleep(3)

            # Enhanced list of username taken error messages
            username_taken_messages = [
                'That username is taken. Try another.',
                'Username not available',
                'This username is already taken',
                'Choose a different username',
                'Sorry, this username isn\'t available',
                'Try a different username',
                'Username is not available',
                'This username isn\'t available',
                'Try another username',
                'Username already exists',
                'Please choose a different username'
            ]

            username_taken = False
            detected_message = None

            # Check for explicit error messages
            for message in username_taken_messages:
                try:
                    if await page.get_by_text(message).is_visible(timeout=1000):
                        username_taken = True
                        detected_message = message
                        logging.info(f"Username conflict detected: {message}")
                        break
                except:
                    continue

            # Check for visual indicators of username conflict
            if not username_taken:
                username_taken = await self._check_visual_conflict_indicators(page)

            if username_taken:
                result['conflict_detected'] = True
                logging.info(f"Username '{original_username}' is taken. Looking for suggestions...")

                # Take screenshot of conflict state
                await self._take_screenshot(page, account_id, "username_conflict_detected")

                # Try to find and select suggested usernames
                suggestion_success = await self._select_username_suggestion(page, user_data, result)

                if suggestion_success:
                    result['username_changed'] = True
                    result['final_username'] = user_data['username']
                    logging.info(f"✅ Username conflict resolved: '{original_username}' → '{user_data['username']}'")
                else:
                    # Fallback: generate new username
                    await self._generate_fallback_username(page, user_data, original_username, result)
                    result['username_changed'] = True
                    result['final_username'] = user_data['username']
                    logging.info(f"✅ Username conflict resolved with fallback: '{original_username}' → '{user_data['username']}'")

            return result

        except Exception as e:
            logging.error(f"Error in username conflict handling: {str(e)}")
            result['error'] = str(e)
            return result
    
    async def _check_visual_conflict_indicators(self, page: Page) -> bool:
        """Check for visual indicators of username conflict"""
        try:
            # Look for error styling or suggestion containers
            conflict_indicators = [
                '[role="alert"]',
                '.error-message',
                '[data-error="true"]',
                '.Ekjuhf',  # Google's error class
                'button[data-value*="@gmail.com"]',
                '[role="button"]:has-text("@gmail.com")',
                'button:has-text("available")',
                '.username-suggestion',
                '[aria-describedby*="error"]'
            ]

            for indicator in conflict_indicators:
                try:
                    elements = page.locator(indicator)
                    if await elements.count() > 0:
                        # Check if it's actually related to username conflict
                        for i in range(await elements.count()):
                            element_text = await elements.nth(i).text_content()
                            if element_text and any(keyword in element_text.lower()
                                                  for keyword in ['taken', 'available', 'try', 'choose', '@gmail.com']):
                                logging.info(f"Username conflict detected via indicator: {indicator}")
                                return True
                except:
                    continue
        except:
            pass
        
        return False
    
    async def _select_username_suggestion(self, page: Page, user_data: Dict[str, Any], result: Dict[str, Any]) -> bool:
        """
        Try to select a suggested username from Gmail's suggestions

        Args:
            page: Playwright page object
            user_data: User data dictionary to update
            result: Result dictionary to update

        Returns:
            True if suggestion was successfully selected, False otherwise
        """
        try:
            # Enhanced selectors for username suggestions
            suggestion_selectors = [
                'button[data-value*="@gmail.com"]',  # Most common
                '[role="button"][data-value*="@gmail.com"]',
                'button:has-text("@gmail.com")',
                '[role="button"]:has-text("@gmail.com")',
                'button:has-text("available")',
                '.username-suggestion button',
                '.suggestion-button',
                'button[jsaction*="click"]:has-text("@gmail.com")',
                'div[role="button"][data-value*="@gmail.com"]',
                '[data-value*="@gmail.com"][role="button"]',
                'button[aria-label*="available"]',
                '.VfPpkd-LgbsSe:has-text("@gmail.com")'  # Google Material button
            ]

            suggestions_found = 0
            selected_suggestion = None

            for selector in suggestion_selectors:
                try:
                    suggestions = page.locator(selector)
                    count = await suggestions.count()
                    suggestions_found = max(suggestions_found, count)

                    if count > 0:
                        logging.info(f"Found {count} username suggestions with selector: {selector}")

                        # Try to get all suggestions for logging
                        all_suggestions = []
                        for i in range(min(count, 5)):  # Limit to first 5 suggestions
                            try:
                                suggestion_text = await suggestions.nth(i).text_content()
                                if suggestion_text:
                                    all_suggestions.append(suggestion_text.strip())
                            except:
                                continue

                        if all_suggestions:
                            logging.info(f"Available username suggestions: {all_suggestions}")

                        # Select the first available suggestion
                        first_suggestion = suggestions.first
                        suggestion_text = await first_suggestion.text_content()

                        if suggestion_text:
                            suggestion_text = suggestion_text.strip()
                            logging.info(f"Selecting username suggestion: {suggestion_text}")

                            # Click the suggestion
                            await first_suggestion.click()
                            await asyncio.sleep(1)  # Wait for selection to register

                            # Extract username from suggestion
                            if '@gmail.com' in suggestion_text:
                                new_username = suggestion_text.replace('@gmail.com', '').strip()
                            else:
                                new_username = suggestion_text.strip()

                            # Update user data
                            user_data['username'] = new_username
                            selected_suggestion = suggestion_text

                            # Update result
                            result['resolution_method'] = 'suggestion_selected'
                            result['suggestions_found'] = suggestions_found
                            result['selected_suggestion'] = selected_suggestion

                            logging.info(f"✅ Successfully selected suggestion: {new_username}")
                            return True

                except Exception as e:
                    logging.debug(f"Suggestion selector {selector} failed: {str(e)}")
                    continue

            result['suggestions_found'] = suggestions_found
            if suggestions_found > 0:
                logging.warning(f"Found {suggestions_found} suggestions but couldn't select any")
            else:
                logging.warning("No username suggestions found")

            return False

        except Exception as e:
            logging.error(f"Error selecting username suggestion: {str(e)}")
            return False
    
    async def _take_screenshot(self, page: Page, account_id: int, step: str):
        """Take screenshot for debugging"""
        try:
            from config import Config
            screenshot_path = Config.SCREENSHOTS_DIR / f"account_{account_id}_{step}.png"
            await page.screenshot(path=str(screenshot_path))
            logging.info(f"Screenshot saved: {screenshot_path}")
        except Exception as e:
            logging.error(f"Failed to take screenshot: {e}")

    async def _generate_fallback_username(self, page: Page, user_data: Dict[str, Any], original_username: str, result: Dict[str, Any]) -> None:
        """
        Generate a fallback username when suggestions are not available

        Args:
            page: Playwright page object
            user_data: User data dictionary to update
            original_username: Original username that was taken
            result: Result dictionary to update
        """
        try:
            # Try multiple fallback strategies
            fallback_strategies = [
                lambda u: f"{u}{random.randint(100, 999)}",
                lambda u: f"{u}.{random.randint(10, 99)}",
                lambda u: f"{u}{random.randint(1000, 9999)}",
                lambda u: f"{u}.{random.randint(1, 9)}",
                lambda u: f"{u}{random.randint(10, 99)}",
            ]

            for i, strategy in enumerate(fallback_strategies):
                try:
                    new_username = strategy(original_username)
                    logging.info(f"Trying fallback username strategy {i+1}: {new_username}")

                    # Clear the username field and enter new username
                    username_selectors = [
                        'input[name="username"]',
                        'input[aria-label*="Username"]',
                        'input[aria-label*="Gmail"]',
                        'input[type="text"]:visible'
                    ]

                    username_field = None
                    for selector in username_selectors:
                        try:
                            field = page.locator(selector).first
                            if await field.is_visible():
                                username_field = field
                                break
                        except:
                            continue

                    if username_field:
                        await username_field.clear()
                        await username_field.fill(new_username)
                        user_data['username'] = new_username

                        result['resolution_method'] = f'fallback_strategy_{i+1}'
                        result['fallback_username'] = new_username

                        logging.info(f"✅ Generated fallback username: {new_username}")
                        return
                    else:
                        logging.warning("Could not find username field for fallback")

                except Exception as e:
                    logging.debug(f"Fallback strategy {i+1} failed: {str(e)}")
                    continue

            # Last resort: use original username with timestamp
            timestamp = str(int(time.time()))[-4:]  # Last 4 digits of timestamp
            final_username = f"{original_username}{timestamp}"
            user_data['username'] = final_username
            result['resolution_method'] = 'timestamp_fallback'
            result['fallback_username'] = final_username

            logging.info(f"✅ Using timestamp fallback username: {final_username}")

        except Exception as e:
            logging.error(f"Error generating fallback username: {str(e)}")
            # Keep original username as last resort
            result['resolution_method'] = 'no_change'
