"""
Gmail Auto Registration - Stealth Mode
Using Playwright with stealth mode to avoid detection
"""

import asyncio
import sys
import json
import click
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any
import logging
from dotenv import load_dotenv

# Import our modules
from config import Config
from user_data_generator import UserDataGenerator
from proxy_manager import ProxyManager
from browser_profile import BrowserProfileGenerator
from src.registration.gmail_registration import GmailRegistration
from src.browser.browser_actions import BrowserActions
from src.utils.screenshot_utils import cleanup_old_screenshots
from constants import Messages

class GmailRegistrationOrchestrator:
    """Main orchestrator for Gmail registration system"""
    
    def __init__(self):
        self.config = Config
        self.proxy_manager = None
        self.user_generator = UserDataGenerator()
        self.browser_generator = BrowserProfileGenerator()
        self.browser_actions = BrowserActions()
        self.gmail_registration = GmailRegistration(self.browser_actions)
        self.results = []
        load_dotenv()
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('registration.log'),
                logging.StreamHandler()
            ]
        )
        
    async def initialize(self) -> bool:
        """
        Initialize the system
        
        Returns:
            bool: True if initialization successful
        """
        try:
            logging.info(Messages.CONSOLE["init_start"])
            
            # Ensure directories exist
            self.config.ensure_directories()
            
            # Validate configuration
            if not self.config.validate_config():
                return False
            
            # Force disable proxy for testing
            self.config.USE_PROXY = False

            # Initialize proxy manager
            if self.config.USE_PROXY:
                self.proxy_manager = ProxyManager()
                if not await self.proxy_manager.initial_setup():
                    logging.error(Messages.CONSOLE["proxy_failed"])
                    return False
            else:
                logging.info(Messages.CONSOLE["proxy_disabled"])
            
            # Print configuration summary
            self.config.print_config_summary()
            
            logging.info(Messages.CONSOLE["init_success"])
            return True
            
        except Exception as e:
            logging.error(f"{Messages.CONSOLE['init_failed']}: {e}")
            return False
    
    async def register_single_account(self, account_id: int = 1) -> Dict[str, Any]:
        """
        Register a single Gmail account
        
        Args:
            account_id: ID of the account (for logging)
            
        Returns:
            Dict containing registration results
        """
        try:
            # Generate user data
            user_data = self.user_generator.generate_user_info()
            logging.info(f"{Messages.CONSOLE['user_generated']}: {user_data['full_name']} ({user_data['username']})")
            
            # Get proxy if enabled
            proxy_config = None
            if self.proxy_manager:
                proxy_config = await self.proxy_manager.get_working_proxy()
                if not proxy_config:
                    return {
                        "success": False,
                        "error": Messages.ERRORS["no_proxy"],
                        "account_id": account_id,
                        "user_data": user_data,
                        "timestamp": datetime.now().isoformat()
                    }
                logging.info(f"{Messages.CONSOLE['proxy_using']}: {proxy_config.server}")
            
            # Create browser profile
            browser_profile = self.browser_generator.create_human_browser_profile(proxy_config)
            logging.info(f"🌍 Browser timezone: {browser_profile['timezone_id']}")
            logging.info(f"📱 Viewport: {browser_profile['viewport']}")
            
            # Start registration process
            result = await self.gmail_registration.register_account(user_data, browser_profile, account_id)
            
            # Save results
            await self._save_account_data(result)
            
            return result
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "account_id": account_id,
                "timestamp": datetime.now().isoformat()
            }
    
    async def register_batch_accounts(self, num_accounts: int, max_concurrent: int = None) -> List[Dict[str, Any]]:
        """
        Register multiple accounts concurrently
        
        Args:
            num_accounts: Number of accounts to register
            max_concurrent: Maximum concurrent registrations
            
        Returns:
            List of registration results
        """
        max_workers = max_concurrent or self.config.MAX_CONCURRENT_REGISTRATIONS
        semaphore = asyncio.Semaphore(max_workers)
        
        async def register_with_semaphore(account_id: int):
            async with semaphore:
                return await self.register_single_account(account_id)
        
        tasks = [register_with_semaphore(i) for i in range(1, num_accounts + 1)]
        results = await asyncio.gather(*tasks)
        
        # Save batch results
        await self._save_batch_results(results)
        
        return results
    
    async def _save_account_data(self, account_data: Dict[str, Any]):
        """Save account data to file"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"account_{'unknown' if not account_data.get('username') else account_data['username']}_{timestamp}.json"
            
            filepath = self.config.ACCOUNTS_DIR / filename
            with open(filepath, 'w') as f:
                json.dump(account_data, f, indent=2)
                
        except Exception as e:
            logging.error(f"Failed to save account data: {str(e)}")
    
    async def _save_batch_results(self, results: List[Dict[str, Any]]):
        """Save batch registration results"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"batch_results_{timestamp}.json"
            
            filepath = self.config.DATA_DIR / filename
            with open(filepath, 'w') as f:
                json.dump({
                    "timestamp": timestamp,
                    "total_accounts": len(results),
                    "successful": sum(1 for r in results if r["success"]),
                    "results": results
                }, f, indent=2)
                
        except Exception as e:
            logging.error(f"Failed to save batch results: {str(e)}")

@click.command()
@click.option('--accounts', '-n', default=1, help='Number of accounts to register')
@click.option('--concurrent', '-c', default=None, type=int, help='Max concurrent registrations')
@click.option('--test-mode', '-t', is_flag=True, help='Run in test mode (no actual registration)')
@click.option('--config-check', is_flag=True, help='Check configuration and exit')
@click.option('--proxy-test', is_flag=True, help='Test proxies and exit')
def main(accounts: int, concurrent: int, test_mode: bool, config_check: bool, proxy_test: bool):
    """Gmail Auto Registration CLI"""
    
    async def run_async():
        orchestrator = GmailRegistrationOrchestrator()
        
        # Initialize system
        if not await orchestrator.initialize():
            sys.exit(1)
            
        # Handle different modes
        if config_check:
            sys.exit(0)
            
        if proxy_test and orchestrator.proxy_manager:
            await orchestrator.proxy_manager.test_all_proxies()
            sys.exit(0)
            
        if test_mode:
            logging.info("🧪 Running in test mode")
            sys.exit(0)
        
        # Clean up old screenshots
        cleanup_old_screenshots()
        
        # Start registration
        if accounts > 1:
            results = await orchestrator.register_batch_accounts(accounts, concurrent)
            successful = sum(1 for r in results if r["success"])
            logging.info(f"✅ Completed batch registration: {successful}/{accounts} successful")
        else:
            result = await orchestrator.register_single_account()
            if result["success"]:
                logging.info("✅ Account registration completed successfully")
            else:
                logging.error(f"❌ Registration failed: {result.get('error', 'Unknown error')}")
    
    # Run async code
    asyncio.run(run_async())

if __name__ == "__main__":
    main()