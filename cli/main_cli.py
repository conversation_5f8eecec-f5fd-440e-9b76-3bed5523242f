"""
Main CLI Module
Command-line interface for Gmail registration tool
"""

import asyncio
import sys
import click
from core import GmailRegistrationOrchestrator


@click.command()
@click.option('--accounts', '-n', default=1, help='Number of accounts to register')
@click.option('--concurrent', '-c', default=None, type=int, help='Max concurrent registrations')
@click.option('--test-mode', '-t', is_flag=True, help='Run in test mode (no actual registration)')
@click.option('--config-check', is_flag=True, help='Check configuration and exit')
@click.option('--proxy-test', is_flag=True, help='Test proxies and exit')
@click.option('--system-test', is_flag=True, help='Test all system components and exit')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
def main(accounts: int, concurrent: int, test_mode: bool, config_check: bool, 
         proxy_test: bool, system_test: bool, verbose: bool):
    """Gmail Auto Registration Tool"""
    
    async def run_async():
        orchestrator = GmailRegistrationOrchestrator()
        
        # Initialize system
        if not await orchestrator.initialize():
            sys.exit(1)
            
        # Handle special modes
        if config_check:
            print("✅ Configuration check passed")
            return
            
        if system_test:
            print("🧪 Testing system components...")
            test_results = await orchestrator.test_system_components()
            
            print("\n📊 System Test Results:")
            for component, status in test_results.items():
                status_icon = "✅" if status else "❌"
                print(f"  {status_icon} {component.replace('_', ' ').title()}: {'PASS' if status else 'FAIL'}")
            
            all_passed = all(test_results.values())
            if all_passed:
                print("\n✅ All system components are working correctly!")
            else:
                print("\n❌ Some system components failed. Please check the configuration.")
                sys.exit(1)
            return
            
        if proxy_test:
            if not orchestrator.proxy_manager:
                print("❌ Proxy testing requires proxy support to be enabled")
                return
            await orchestrator.proxy_manager.test_all_proxies()
            return
            
        if test_mode:
            print("🧪 Running in TEST MODE - no actual registrations will be performed")
            
        # Start registration process
        if accounts > 1:
            results = await orchestrator.register_batch_accounts(accounts, concurrent)
            success_count = len([r for r in results if r["success"]])
            print(f"\n✨ Batch registration completed: {success_count}/{accounts} successful")
            
            # Exit with error code if no accounts were successfully registered
            if success_count == 0:
                sys.exit(1)
        else:
            result = await orchestrator.register_single_account()
            if result["success"]:
                print("\n✨ Registration completed successfully!")
            else:
                print("\n❌ Registration failed!")
                sys.exit(1)
    
    # Run async code
    asyncio.run(run_async())


@click.group()
def cli():
    """Gmail Auto Registration Tool - Advanced Commands"""
    pass


@cli.command()
@click.option('--accounts', '-n', default=5, help='Number of test accounts to generate')
def generate_test_data(accounts: int):
    """Generate test user data without registration"""
    from user_data_generator import UserDataGenerator
    
    generator = UserDataGenerator()
    print(f"🧪 Generating {accounts} test user profiles...")
    
    for i in range(1, accounts + 1):
        user_data = generator.generate_user_info()
        print(f"\n👤 Test User #{i}:")
        print(f"  Name: {user_data['full_name']}")
        print(f"  Username: {user_data['username']}")
        print(f"  Birth Date: {user_data['birth_date']}")
        print(f"  Gender: {user_data['gender']}")
        print(f"  City: {user_data['city']}")
        print(f"  Phone: {user_data['phone']}")


@cli.command()
def test_browser_profiles():
    """Test browser profile generation"""
    from browser_profile import BrowserProfileGenerator
    
    generator = BrowserProfileGenerator()
    print("🌐 Testing browser profile generation...")
    
    for i in range(3):
        profile = generator.create_human_browser_profile()
        print(f"\n🖥️ Profile #{i+1}:")
        print(f"  User Agent: {profile['user_agent'][:80]}...")
        print(f"  Viewport: {profile['viewport']}")
        print(f"  Timezone: {profile['timezone_id']}")
        print(f"  Locale: {profile['locale']}")


@cli.command()
@click.option('--proxy-file', help='Path to proxy file to test')
def test_proxies(proxy_file: str):
    """Test proxy connections"""
    async def test_proxy_async():
        from proxy_manager import ProxyManager
        
        proxy_manager = ProxyManager()
        if proxy_file:
            # Test specific proxy file
            print(f"🔍 Testing proxies from: {proxy_file}")
            # Implementation would load and test proxies from file
        else:
            # Test configured proxies
            print("🔍 Testing configured proxies...")
            await proxy_manager.test_all_proxies()
    
    asyncio.run(test_proxy_async())


@cli.command()
@click.option('--output', '-o', help='Output file for statistics')
def show_stats(output: str):
    """Show registration statistics"""
    import json
    from pathlib import Path
    from utils import RegistrationUtils
    from config import Config
    
    utils = RegistrationUtils(Config)
    
    # Load recent results
    results_dir = Config.DATA_DIR
    result_files = list(results_dir.glob("batch_results_*.json"))
    
    if not result_files:
        print("❌ No registration results found")
        return
    
    # Load the most recent results
    latest_file = max(result_files, key=lambda f: f.stat().st_mtime)
    
    with open(latest_file, 'r') as f:
        results = json.load(f)
    
    print(f"📊 Statistics from: {latest_file.name}")
    utils.print_registration_summary(results)
    
    if output:
        stats = utils.calculate_registration_stats(results)
        with open(output, 'w') as f:
            json.dump(stats, f, indent=2)
        print(f"📁 Statistics saved to: {output}")


@cli.command()
def cleanup():
    """Clean up temporary files and logs"""
    from config import Config
    import shutil
    
    print("🧹 Cleaning up temporary files...")
    
    # Clean screenshots older than 7 days
    screenshots_dir = Config.SCREENSHOTS_DIR
    if screenshots_dir.exists():
        import time
        current_time = time.time()
        week_ago = current_time - (7 * 24 * 60 * 60)  # 7 days in seconds
        
        cleaned_count = 0
        for screenshot in screenshots_dir.glob("*.png"):
            if screenshot.stat().st_mtime < week_ago:
                screenshot.unlink()
                cleaned_count += 1
        
        print(f"🗑️ Cleaned {cleaned_count} old screenshots")
    
    # Clean old log files
    logs_dir = Config.LOGS_DIR
    if logs_dir.exists():
        for log_file in logs_dir.glob("*.log.*"):  # Rotated log files
            if log_file.stat().st_mtime < week_ago:
                log_file.unlink()
        print("🗑️ Cleaned old log files")
    
    print("✅ Cleanup completed")


if __name__ == "__main__":
    main()
